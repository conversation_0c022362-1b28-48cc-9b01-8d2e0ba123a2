using UnityEditor;
using UnityEngine;

public class SpiderRagdollBuilder : MonoBehaviour
{
    [MenuItem("Tools/Spider/Build Spider Ragdoll Clean")]
    static void BuildSpiderRagdoll()
    {
        var root = Selection.activeTransform;
        if (root == null)
        {
            Debug.LogError("Select the 'spiderrig' GameObject.");
            return;
        }

        var bones = root.GetComponentsInChildren<Transform>(true);

        // Clean up: destroy joints first
        foreach (var t in bones)
        {
            Object.DestroyImmediate(t.GetComponent<CharacterJoint>());
            Object.DestroyImmediate(t.GetComponent<ConfigurableJoint>());
        }
        foreach (var t in bones)
        {
            Object.DestroyImmediate(t.GetComponent<Rigidbody>());
            Object.DestroyImmediate(t.GetComponent<Collider>());
        }

        var spine = FindTransformByName(bones, "Spine_01");
        if (spine == null)
        {
            Debug.LogError("Could not find 'Spine_01'");
            return;
        }

        var spineRB = spine.gameObject.AddComponent<Rigidbody>();
        spineRB.mass = 3f;

        var spineCol = spine.gameObject.AddComponent<CapsuleCollider>();
        spineCol.radius = 0.5f;
        spineCol.height = 1f;
        spineCol.direction = 1;
        spineCol.center = Vector3.zero;

        foreach (var t in bones)
        {
            if (!t.name.Contains("-2_")) continue;

            string legPrefix = t.name.Substring(0, t.name.IndexOf("-2_"));

            var knee = FindTransformByName(bones, legPrefix + "-3_" + GetSuffix(t.name));
            var foot = FindTransformByName(bones, legPrefix + "-3_" + GetSuffix(t.name) + ".001");

            // Segment 1: foot to knee → collider on `knee`
            if (foot && knee)
            {
                var footRB = knee.gameObject.AddComponent<Rigidbody>();
                footRB.mass = 1f;

                AddCapsuleBetween(knee, foot, knee.gameObject);
                AddJoint(knee, spineRB);
            }

            // Segment 2: knee to hip → collider on `hip`
            if (t && knee)
            {
                var hipRB = t.gameObject.AddComponent<Rigidbody>();
                hipRB.mass = 0.5f;

                AddCapsuleBetween(t, knee, t.gameObject);
                AddJoint(t, spineRB);
            }
        }

        Debug.Log("✅ Spider ragdoll built with 17 colliders.");
    }

    static void AddCapsuleBetween(Transform a, Transform b, GameObject target)
    {
        var dir = b.position - a.position;
        float dist = dir.magnitude;

        var col = target.AddComponent<CapsuleCollider>();
        col.height = dist;
        col.radius = Mathf.Clamp(dist * 0.2f, 0.01f, 0.05f);
        col.direction = 1;
        col.center = target.transform.InverseTransformPoint((a.position + b.position) / 2f);
    }

    static void AddJoint(Transform part, Rigidbody connected)
    {
        var joint = part.gameObject.AddComponent<CharacterJoint>();
        joint.connectedBody = connected;
    }

    static Transform FindTransformByName(Transform[] all, string name)
    {
        foreach (var t in all)
            if (t.name == name)
                return t;
        return null;
    }

    static string GetSuffix(string name)
    {
        if (name.EndsWith("_L")) return "L";
        if (name.EndsWith("_R")) return "R";
        return "";
    }
}
