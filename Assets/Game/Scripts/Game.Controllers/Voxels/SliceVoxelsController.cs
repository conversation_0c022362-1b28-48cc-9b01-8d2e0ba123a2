using System;
using System.Collections.Generic;
using System.Reactive;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.Voxels;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Voxels
{
    public class SliceVoxelsController : ControllerBase
    {
        private VoxelModel voxelModel;
        private INetworkClient networkClient;

        private List<SliceVoxel> SliceVoxels => voxelModel.SliceVoxels;

        [Inject]
        private void Construct(PlayersModel playersModel, LevelModel levelModel, VoxelModel voxelModel, VoxelSpaceManager voxelSpaceManager, INetworkClient networkClient)
        {
            this.voxelModel = voxelModel;
            this.networkClient = networkClient;

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
            levelModel.OnMapSaved.Subscribe(HandleMapSaved).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnInitialVoxelCreating.Subscribe(HandleVoxelCreating).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnVoxelCreating.Subscribe(HandleVoxelCreating).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnInitialVoxelDestroying.Subscribe(HandleVoxelDestroying).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnVoxelDestroying.Subscribe(HandleVoxelDestroying).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                SliceVoxels.Clear();
            }
        }

        private void HandleVoxelCreating(CreatingVoxelArgs args)
        {
            var voxel = SliceVoxels.Find(x => x.position == args.position);

            if (voxel == null)
            {
                SliceVoxels.Add(new SliceVoxel
                {
                    position = args.position,
                    id = args.id,
                    isDestroyed = false,
                    time = args.time,
                    rotation = args.rotation
                });
            }
            else
            {
                voxel.id = args.id;
                voxel.isDestroyed = false;
                voxel.time = args.time;
                voxel.rotation = args.rotation;
            }
        }

        private void HandleVoxelDestroying(DestroyingVoxelArgs args)
        {
            var voxel = SliceVoxels.Find(x => x.position == args.position);

            if (voxel == null)
            {
                SliceVoxels.Add(new SliceVoxel
                {
                    position = args.position,
                    id = 0,
                    isDestroyed = true,
                    time = args.time
                });
            }
            else
            {
                voxel.id = 0;
                voxel.isDestroyed = true;
                voxel.time = args.time;
            }
        }

        private void HandleMapSaved(Unit unit)
        {
            var time = Mathf.Max(0, networkClient.ServerTime - 5);

            for (var i = 0; i < SliceVoxels.Count; i++)
            {
                var voxel = SliceVoxels[i];

                if (voxel.time > time)
                {
                    continue;
                }

                SliceVoxels.Remove(voxel);
            }

            GameLogger.Voxels.Debug("Slice voxels sliced when map was saved.");
        }
    }
}