using System;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Models;
using Game.Views.Voxels;
using Modules.Core;
using Modules.Network;
using Modules.XR;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Voxels
{
    public class ApplySliceMapController : ControllerBase
    {
        private const int VoxelCountInFrame = 256;

        private IXRPlayer xrPlayer;
        private INetworkClient networkClient;
        private VoxelSpaceManager voxelSpaceManager;

        [Inject]
        private void Construct(VoxelModel voxelModel, VoxelSpaceManager voxelSpaceManager, INetworkClient networkClient, IXRPlayer xrPlayer)
        {
            this.xrPlayer = xrPlayer;
            this.networkClient = networkClient;
            this.voxelSpaceManager = voxelSpaceManager;

            voxelModel.OnSliceMapReceived.Subscribe(sliceMap => HandleSliceMap(sliceMap).Forget()).AddTo(DisposeCancellationToken);
        }

        private async UniTaskVoid HandleSliceMap(SliceMap sliceMap)
        {
            if (sliceMap == null)
            {
                return;
            }

            GameLogger.Voxels.Debug("Start to apply slice map");

            var voxelCounter = 0;
            var sliceVoxels = sliceMap.voxels;
            var origin = xrPlayer.BodyPosition;
            sliceVoxels.Sort((x, y) => GetSqrDistance(x.position, origin).CompareTo(GetSqrDistance(y.position, origin)));

            foreach (var voxel in sliceVoxels)
            {
                if (voxel.isDestroyed)
                {
                    voxelSpaceManager.DamageVoxel(voxel.position, byte.MaxValue, false);
                }
                else
                {
                    voxelSpaceManager.CreateVoxel(voxel.position, voxel.id, voxel.rotation, false);
                }

                if (voxelCounter++ > VoxelCountInFrame)
                {
                    voxelCounter = 0;
                    await UniTask.Yield(networkClient.DisconnectionCancellationToken);
                }
            }

            GameLogger.Voxels.Debug("Finished to apply slice map");
        }

        private static float GetSqrDistance(Vector3 start, Vector3 end)
        {
            return (start - end).sqrMagnitude;
        }
    }
}