using System;
using Cysharp.Threading.Tasks;
using Game.Models;
using Game.Services;
using Game.Views.Moderation;
using Game.Views.Players;
using Game.Views.UI.Screens.Modal;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Moderation
{
    public class ModerationApplyController : ControllerBase
    {
        private GameModel gameModel;
        private ModalScreen modalScreen;
        private INotificationService notificationService;
        private PlayersModel playersModel;

        private bool CanUnmuteOtherPlayer => gameModel.IsPlayerModerator() || gameModel.IsPlayerModeratorTrainee();

        [Inject]
        private void Construct(ModerationModel moderationModel, GameModel gameModel, IScreenManager screenManager, INotificationService notificationService, PlayersModel playersModel)
        {
            this.gameModel = gameModel;
            this.notificationService = notificationService;
            this.playersModel = playersModel;
            modalScreen = screenManager.GetScreen<ModalScreen>();

            moderationModel.OnModerationApplied.Subscribe(HandleModerationApplied).AddTo(DisposeCancellationToken);
            moderationModel.OnModerationSentenceApplied.Subscribe(HandleModerationSentenceApplied).AddTo(DisposeCancellationToken);
        }

        private void HandleModerationSentenceApplied(ModerationSentenceArgs args)
        {
            if (playersModel.TryGetPlayer(args.playerId, out var player))
            {
                ApplySentence(args.sentence, player);
            }
        }

        private void HandleModerationApplied(ModerationApplyArgs args)
        {
            var player = args.player;
            var moderationStick = args.moderationStick;
            var sentence = moderationStick.ActiveSentence;

            if (ApplySentence(sentence, player))
            {
                moderationStick.PlayHitAudio();
            }
        }

        private bool ApplySentence(ModerationSentence sentence, PlayerActor player)
        {
            if (sentence == ModerationSentence.MuteForSelf)
            {
                if (!player.IsMuteLocal)
                {
                    player.MuteVoiceLocal(true);
                    return true;
                }

                return false;
            }

            if (sentence is ModerationSentence.MuteForSession or ModerationSentence.MuteForDay or ModerationSentence.MuteForThreeDays or ModerationSentence.MuteForYear && player.IsMutedByAdmin)
            {
                return false;
            }

            if (sentence == ModerationSentence.UnmutePlayer)
            {
                if (player.IsMuteLocal)
                {
                    player.MuteVoiceLocal(false);
                    return true;
                }

                if (!player.IsMutedByAdmin || !CanUnmuteOtherPlayer)
                {
                    return false;
                }
            }

            if (sentence is ModerationSentence.SendToPrisonFiveMinutes or ModerationSentence.SendToPrisonFifteenMinutes && player.IsInPrison)
            {
                return false;
            }

            if (sentence == ModerationSentence.TakePlayerOutOfPrison && !player.IsInPrison)
            {
                return false;
            }

            modalScreen.Render(
                    $"Are you sure you want to apply {sentence} to {player.Name}?",
                    "Yes",
                    "No",
                    () =>
                    {
                        player.SetModerationRpc(sentence);
                        SendNotification(sentence, player);
                        modalScreen.Hide();
                    },
                    () => modalScreen.Hide())
                .WithTimeout(15, () => modalScreen.Hide());
            return true;
        }

        private void SendNotification(ModerationSentence moderationSentence, PlayerActor player)
        {
            var moderator = gameModel.UserName;
            var reportedPlayer = player.Name.Value;
            var action = moderationSentence.ToString();
            notificationService.SendModerationAction(moderator, reportedPlayer, action);
        }
    }
}