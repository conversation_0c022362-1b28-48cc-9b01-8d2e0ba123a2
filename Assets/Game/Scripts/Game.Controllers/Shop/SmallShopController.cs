using System;
using System.Collections.Generic;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Game.Models;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.SmallShop;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Shop
{
    public class SmallShopController : ControllerBase
    {
        private LevelModel levelModel;
        private EconomyModel economyModel;
        private SmallShopManager smallShopManager;
        private LevelSpaceManager levelSpaceManager;
        private LobbySpaceManager lobbySpaceManager;

        [Inject]
        private void Construct(LevelModel levelModel, EconomyModel economyModel, SmallShopManager smallShopManager, LobbySpaceManager lobbySpaceManager, LevelSpaceManager levelSpaceManager)
        {
            this.levelModel = levelModel;
            this.economyModel = economyModel;
            this.smallShopManager = smallShopManager;
            this.lobbySpaceManager = lobbySpaceManager;
            this.levelSpaceManager = levelSpaceManager;

            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
            economyModel.OnPurchaseCompleted.Subscribe(_ => HandlePurchaseCompeted()).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded()
        {
            smallShopManager.DestroyAll();

            var poses = new List<Pose>();
            if (levelModel.IsLobbyLevel)
            {
                poses.AddRange(lobbySpaceManager.SmallShopNode.GetChildPoses());
            }
            else if (levelModel.IsMinesLevel)
            {
                poses.AddRange(levelSpaceManager.SmallShopNode.GetChildPoses());
            }

            smallShopManager.Create(poses, economyModel.InventoryList);
        }

        private void HandlePurchaseCompeted()
        {
            smallShopManager.Refresh();
        }
    }
}