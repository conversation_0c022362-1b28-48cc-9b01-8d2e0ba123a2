using System;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Views.Avatars;
using Game.Views.Levels;
using Game.Views.Monsters;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using UnityEngine.AI;
using VContainer;
using Random = UnityEngine.Random;

namespace Game.Controllers.Monsters
{
    public class MonsterBrainController : BaseMonsterBrainController
    {
        [SerializeField] private NavMeshAgent navMeshAgent;

        private float nextRotateToTargetTime;
        private bool isStuck;
        private float nextPathUpdateTime;
        private const float pathUpdateInterval = 0.2f;

        private float nextTargetSwitchTime;
        private const float targetSwitchInterval = 5f;

        private float patrolBoundsXMin, patrolBoundsXMax, patrolBoundsZMin, patrolBoundsZMax, patrolBoundsYMin, patrolBoundsYMax;
        private bool hasPatrolArea;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            MonstersConfig monstersConfig,
            PlayersModel playersModel,
            ISubscriber<MonsterDamageArgs> damageSubscriber,
            IAudioClient audioClient,
            AvatarsConfig avatarsConfig)
        {
            this.levelModel = levelModel;
            this.monstersConfig = monstersConfig;
            this.playersModel = playersModel;
            this.audioClient = audioClient;
            this.avatarsConfig = avatarsConfig;

            damageSubscriber.Subscribe(HandleDamage).AddTo(destroyCancellationToken);
        }

        public override void Render()
        {
            base.Render();
            if (IsAlive)
            {
                PlayRandomVoice();
            }

            if (!Runner.IsSharedModeMasterClient)
            {
                return;
            }

            if ((monster.StateAuthority == PlayerRef.Invalid || monster.StateAuthority == PlayerRef.None) && Time.time >= nextPlayerCheckTime)
            {
                nextPlayerCheckTime = Time.time + 2f;
                Debug.LogWarning("[MonsterBrainController]: Object lost State Authority! Requesting transfer to master client...");
                TransferAuthority().Forget();
            }
        }

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();
            ScanTarget();
            UpdateStates();
            TryPlaceOnNavMesh();
        }

        public override void Spawned()
        {
            base.Spawned();
            if (monstersConfig.TryGetMonsterData(monster.AvatarId, out var data))
            {
                monsterData = data;
            }

            if (levelModel.Level.monsters != null && monster.MonsterDataIndex < levelModel.Level.monsters.Count)
            {
                var monsterSpawnData = levelModel.Level.monsters[monster.MonsterDataIndex];
                hasPatrolArea = monsterSpawnData.HasPatrolArea;
                if (hasPatrolArea)
                {
                    patrolBoundsXMin = monsterSpawnData.patrolCenter.x - 0.5f * Math.Abs(monsterSpawnData.patrolArea.x);
                    patrolBoundsXMax = monsterSpawnData.patrolCenter.x + 0.5f * Math.Abs(monsterSpawnData.patrolArea.x);

                    patrolBoundsZMin = monsterSpawnData.patrolCenter.z - 0.5f * Math.Abs(monsterSpawnData.patrolArea.z);
                    patrolBoundsZMax = monsterSpawnData.patrolCenter.z + 0.5f * Math.Abs(monsterSpawnData.patrolArea.z);

                    patrolBoundsYMin = monsterSpawnData.patrolCenter.y - 0.5f * Math.Abs(monsterSpawnData.patrolArea.y);
                    patrolBoundsYMax = monsterSpawnData.patrolCenter.y + 0.5f * Math.Abs(monsterSpawnData.patrolArea.y);
                }

                if (HasStateAuthority)
                {
                    navMeshAgent.enabled = false;
                    monster.transform.position = monsterSpawnData.spawn;
                    navMeshAgent.enabled = true;
                }
            }
        }

        private void UpdateStates()
        {
            if (!HasTarget)
            {
                State = MonsterState.Patrol;
                Patrol();
            }
            else
            {
                if (IsInRange())
                {
                    State = MonsterState.Attack;
                    AttachToPlayer();
                    Attack();
                }
                else
                {
                    Chase();
                }
            }

            HandleTransitions();
        }

        private void Chase()
        {
            State = MonsterState.Chase;
            if (!navMeshAgent.isOnNavMesh)
            {
                return;
            }

            navMeshAgent.speed = ChaseSpeed;
            if (Time.time >= nextPathUpdateTime)
            {
                navMeshAgent.SetDestination(targetPlayer.transform.position);
                nextPathUpdateTime = Time.time + pathUpdateInterval;
            }
        }

        private bool IsTargetInPatrolArea(Transform target)
        {
            if (!hasPatrolArea)
            {
                return true;
            }

            if (target == null)
            {
                return false;
            }

            var targetPos = target.position;

            return targetPos.x >= patrolBoundsXMin &&
                   targetPos.x <= patrolBoundsXMax &&
                   targetPos.z >= patrolBoundsZMin &&
                   targetPos.z <= patrolBoundsZMax &&
                   targetPos.y >= patrolBoundsYMin &&
                   targetPos.y <= patrolBoundsYMax;
        }

        private void HandleTransitions()
        {
            // Transitions
            // If we go from Attack to Not Attack
            if (previousFrameState == MonsterState.Attack && State != MonsterState.Attack)
            {
                navMeshAgent.enabled = true;
                if (State != MonsterState.DamageReceive)
                {
                    nextAttackTime = 0;
                }
            }

            // If we go from Not Attack to Attack
            if (previousFrameState != MonsterState.Attack && State == MonsterState.Attack)
            {
                navMeshAgent.enabled = false;
                if (previousFrameState != MonsterState.DamageReceive)
                {
                    nextAttackTime = 0;
                    SetAttackAudioRpc();
                }
            }

            // If we go from Not Chase to Chase
            if (previousFrameState != MonsterState.Chase && State == MonsterState.Chase)
            {
                SetScreamRpc(true);
            }

            // If we go from Chase to not Chase
            if (previousFrameState == MonsterState.Chase && State != MonsterState.Chase)
            {
                SetScreamRpc(false);
            }
        }

        private bool IsInRange()
        {
            if (monsterData == null) return false;
            return Vector3.Distance(targetPlayer.transform.position, monster.transform.position) < monsterData.AttackMinDistance;
        }

        private void AttachToPlayer()
        {
            var targetPosition = targetPlayer.transform.position + targetPlayer.transform.forward * 2f;
            targetPosition.y -= 1.5f;
            monster.transform.position = targetPosition;
            monster.transform.rotation = Quaternion.LookRotation(-targetPlayer.transform.forward.OnlyXZ().normalized, Vector3.up);
        }

        private void ScanTarget()
        {
            if (targetPlayer != null)
            {
                // First try to switch targets based on distance
                if (Time.time >= nextTargetSwitchTime && playersModel.TryFindClosestPlayer(monster.transform.position, monstersConfig.ScanPlayerRadius, IsPlayerTargetable, out var player, 3))
                {
                    nextTargetSwitchTime = Time.time + targetSwitchInterval;
                    SetNewTargetRpc(player);
                    return;
                }

                // If could not switch targets, check if current target is still valid
                if (!IsPlayerTargetable(targetPlayer) || Vector3.Distance(targetPlayer.transform.position, monster.transform.position) > monstersConfig.ScanPlayerRadius)
                {
                    SetNewTargetRpc(null);
                }
            }
            else
            {
                // Always scan for new target if target is null
                if (playersModel.TryFindClosestPlayer(monster.transform.position, monstersConfig.ScanPlayerRadius, IsPlayerTargetable, out var player, 3))
                {
                    if (targetPlayer != player)
                    {
                        SetNewTargetRpc(player);
                    }
                }
            }
        }

        private async UniTaskVoid TransferAuthority()
        {
            navMeshAgent.enabled = false;
            navMeshAgent.updateRotation = false;
            var result = await Object.RequestStateAuthorityAsync(DespawnCancellationToken);
            if (result)
            {
                nextTargetSwitchTime = Time.time + targetSwitchInterval;
                if (targetPlayer != null)
                {
                    var direction = (targetPlayer.transform.position - monster.transform.position).normalized;
                    monster.transform.rotation = Quaternion.LookRotation(direction);
                }

                monster.Teleport(monster.transform.position);
                navMeshAgent.enabled = true;
                navMeshAgent.updateRotation = true;
            }
        }

        private bool IsPlayerTargetable(PlayerActor player)
        {
            if (!player.IsValid)
            {
                return false;
            }

            if (levelModel.LevelConfig.IsInfectedWhenDead)
            {
                return player.IsAlive && !player.IsTagged.Value;
            }
            
            if (avatarsConfig.TryGetAvatarCode(player.AvatarId, out var code) && avatarsConfig.IsAminMonster(code))
            {
                return false;
            }

            if (hasPatrolArea && !IsTargetInPatrolArea(player.transform))
            {
                return false;
            }

            return player.IsAlive;
        }

        private void Patrol()
        {
            if (!navMeshAgent.isOnNavMesh)
            {
                return;
            }

            navMeshAgent.speed = PatrolSpeed;
            if (navMeshAgent.pathStatus == NavMeshPathStatus.PathPartial || navMeshAgent.remainingDistance < 0.5f)
            {
                navMeshAgent.SetDestination(GetRandomNavMeshPoint());
            }
        }

        private void Attack()
        {
            if (nextAttackTime == 0)
            {
                nextAttackTime = Time.time + monstersConfig.InitialAttackDelay;
                return;
            }

            if (IsAttacking)
            {
                return;
            }

            targetPlayer.SetDamageByMonsterRpc(AttackDamage);
            nextAttackTime = Time.time + monstersConfig.AttackInterval;
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void SetScreamRpc(bool scream)
        {
            if (monsterData != null)
            {
                if (scream)
                {
                    monster.PlayAudio(monsterData.ChaseSoundKey, true);
                }
                else
                {
                    audioClient.Stop(monsterData.ChaseSoundKey);
                }
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void SetAttackAudioRpc()
        {
            if (monsterData != null)
            {
                monster.PlayAudio(monsterData.AttackSoundKey, true);
            }
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void SetNewTargetRpc(PlayerActor playerActor)
        {
            targetPlayer = playerActor;
            if (playersModel.LocalPlayer.Value == targetPlayer && !HasStateAuthority)
            {
                TransferAuthority().Forget();
            }
        }

        private void ReceiveDamage()
        {
            if (IsDamageReceiving)
            {
                return;
            }

            State = MonsterState.DamageReceive;
            nextDamageReceiveTime = Time.time + 0.5f;
        }

        private void TryPlaceOnNavMesh()
        {
            if (State == MonsterState.Attack)
            {
                return;
            }

            if (!navMeshAgent.enabled)
            {
                return;
            }

            if (navMeshAgent.isOnNavMesh)
            {
                return;
            }

            if (NavMesh.SamplePosition(monster.transform.position, out var hit, 10f, NavMesh.AllAreas))
            {
                var newPosition = new Vector3(monster.transform.position.x, hit.position.y, monster.transform.position.z);
                monster.transform.position = newPosition;
            }
        }

        private Vector3 GetRandomNavMeshPoint()
        {
            Vector3 searchOrigin;
            float searchRadius;

            if (hasPatrolArea)
            {
                searchRadius = 1f;
                searchOrigin = new Vector3(
                    Random.Range(patrolBoundsXMin, patrolBoundsXMax),
                    transform.position.y,
                    Random.Range(patrolBoundsZMin, patrolBoundsZMax)
                );

                if (NavMesh.SamplePosition(searchOrigin, out var hit, searchRadius, NavMesh.AllAreas))
                {
                    return hit.position;
                }
            }
            else
            {
                searchOrigin = transform.position;
                searchRadius = 10f;
                var randomDirection = Random.insideUnitSphere * 10f;
                randomDirection += searchOrigin;

                if (NavMesh.SamplePosition(randomDirection, out var hit, searchRadius, NavMesh.AllAreas))
                {
                    return hit.position;
                }
            }

            return monster.transform.position;
        }

        private void PlayRandomVoice()
        {
            if (Time.time < nextVoiceTime)
            {
                return;
            }

            if (monsterData != null)
            {
                monster.PlayAudio(monsterData.RandomSoundKey);
            }

            nextVoiceTime = Time.time + Random.Range(5f, 15f);
        }

        private void HandleDamage(MonsterDamageArgs args)
        {
            if (!HasStateAuthority || args.monster != monster || !IsAlive)
            {
                return;
            }

            ReceiveDamage();
        }
    }
}