using Cysharp.Threading.Tasks;
using Fusion;
using Game.Views.Avatars;
using Game.Views.Levels;
using Game.Views.Monsters;
using Game.Views.Players;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;
using Random = UnityEngine.Random;

namespace Game.Controllers.Monsters
{
    public abstract class BaseMonsterBrainController : NetworkActor
    {
        [SerializeField] protected MonsterActor monster;

        protected LevelModel levelModel;
        protected MonstersConfig monstersConfig;
        protected PlayersModel playersModel;
        protected IAudioClient audioClient;
        protected AvatarsConfig avatarsConfig;

        protected float nextDamageReceiveTime;
        protected float nextVoiceTime;
        protected float nextAttackTime;
        protected float nextPlayerCheckTime;

        protected PlayerActor targetPlayer;
        protected MonstersConfig.Data monsterData;

        protected bool HasTarget => targetPlayer != null;
        protected bool IsAttacking => Time.time < nextAttackTime;
        protected bool IsDamageReceiving => Time.time < nextDamageReceiveTime;
        protected bool IsAlive => monster.IsAlive;
        protected float ChaseSpeed => monster.ChaseSpeed;
        protected byte AttackDamage => monster.AttackDamage;
        protected float PatrolSpeed => monster.PatrolSpeed;

        protected MonsterState State
        {
            get => monster.State;
            set
            {
                previousFrameState = monster.State;
                monster.State = value;
            }
        }

        protected MonsterState previousFrameState;

        [Inject]
        private void ConstructBase(
            LevelModel levelModel,
            MonstersConfig monstersConfig,
            PlayersModel playersModel,
            IAudioClient audioClient,
            AvatarsConfig avatarsConfig)
        {
            this.levelModel = levelModel;
            this.monstersConfig = monstersConfig;
            this.playersModel = playersModel;
            this.audioClient = audioClient;
            this.avatarsConfig = avatarsConfig;
        }

        public override void Render()
        {
            base.Render();
            if (IsAlive)
            {
                PlayRandomVoice();
            }

            if (!Runner.IsSharedModeMasterClient)
            {
                return;
            }

            if ((monster.StateAuthority == PlayerRef.Invalid || monster.StateAuthority == PlayerRef.None) && Time.time >= nextPlayerCheckTime)
            {
                nextPlayerCheckTime = Time.time + 2f;
                Debug.LogWarning($"[{GetType().Name}]: Object lost State Authority! Requesting transfer to master client...");
                TransferAuthority().Forget();
            }
        }

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();
            UpdateBehavior();
        }

        public override void Spawned()
        {
            base.Spawned();
            if (monstersConfig.TryGetMonsterData(monster.AvatarId, out var data))
            {
                monsterData = data;
            }

            InitializeSpawnData();
            OnSpawned();
        }

        protected virtual void InitializeSpawnData()
        {
            if (levelModel.Level.monsters != null && monster.MonsterDataIndex < levelModel.Level.monsters.Count)
            {
                var monsterSpawnData = levelModel.Level.monsters[monster.MonsterDataIndex];
                if (HasStateAuthority)
                {
                    monster.transform.position = monsterSpawnData.spawn;
                }
            }
        }

        protected abstract void UpdateBehavior();
        protected abstract void OnSpawned();
        protected abstract UniTaskVoid TransferAuthority();

        protected virtual bool IsPlayerTargetable(PlayerActor player)
        {
            if (!player.IsValid)
            {
                return false;
            }

            if (levelModel.LevelConfig.IsInfectedWhenDead)
            {
                return player.IsAlive && !player.IsTagged.Value;
            }

            if (avatarsConfig.TryGetAvatarCode(player.AvatarId, out var code) && avatarsConfig.IsAminMonster(code))
            {
                return false;
            }

            return player.IsAlive;
        }

        protected virtual bool IsInRange()
        {
            if (monsterData == null) return false;
            return Vector3.Distance(targetPlayer.transform.position, monster.transform.position) < monsterData.AttackMinDistance;
        }

        protected virtual void PlayRandomVoice()
        {
            if (Time.time < nextVoiceTime)
            {
                return;
            }

            if (monsterData != null)
            {
                monster.PlayAudio(monsterData.RandomSoundKey);
            }

            nextVoiceTime = Time.time + Random.Range(5f, 15f);
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        protected void SetScreamRpc(bool scream)
        {
            if (monsterData != null)
            {
                if (scream)
                {
                    monster.PlayAudio(monsterData.ChaseSoundKey, true);
                }
                else
                {
                    audioClient.Stop(monsterData.ChaseSoundKey);
                }
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        protected void SetAttackAudioRpc()
        {
            if (monsterData != null)
            {
                monster.PlayAudio(monsterData.AttackSoundKey, true);
            }
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        protected void SetNewTargetRpc(PlayerActor playerActor)
        {
            targetPlayer = playerActor;
            if (playersModel.LocalPlayer.Value == targetPlayer && !HasStateAuthority)
            {
                TransferAuthority().Forget();
            }
        }

        protected virtual void ReceiveDamage()
        {
            if (IsDamageReceiving)
            {
                return;
            }

            nextDamageReceiveTime = Time.time + 0.25f;
        }

        protected virtual void HandleDamage(MonsterDamageArgs args)
        {
            if (!HasStateAuthority || args.monster != monster || !IsAlive)
            {
                return;
            }

            ReceiveDamage();
        }
    }
}
