using System;
using Cysharp.Threading.Tasks;
using Game.Controllers.Monsters.Features;
using Game.Views.Monsters;
using Game.Views.PlayerEffects;
using Game.Views.Players;
using Game.Views.Voxels;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;
using Random = UnityEngine.Random;

namespace Game.Controllers.Monsters
{
    public class UnifiedMonsterBrainController : BaseMonsterBrainController
    {
        [Header("Movement")]
        [SerializeReference] private IMonsterMovement movement;

        [Header("Attack")]
        [SerializeReference] private IMonsterAttack attack;

        [Header("Behavior")]
        [SerializeField] private bool useIdlePatrolSwitching;
        [SerializeField] private bool attachToPlayerOnAttack;
        [SerializeField] private bool disableNavMeshOnAttack;

        private PlayerEffectsManager effectsManager;
        
        private float nextTargetSwitchTime;
        private const float targetSwitchInterval = 5f;
        private float nextIdleOrWalkSwitchTime;

        [Inject]
        private void Construct(
            ISubscriber<MonsterDamageArgs> damageSubscriber,
            PlayerEffectsManager effectsManager,
            VoxelSpaceManager voxelSpaceManager)
        {
            this.effectsManager = effectsManager;

            // Set VoxelSpaceManager for jumping movement if needed
            if (jumpingMovement != null)
            {
                jumpingMovement.SetVoxelSpaceManager(voxelSpaceManager);
            }

            damageSubscriber.Subscribe(HandleDamage).AddTo(destroyCancellationToken);
        }

        protected override void UpdateBehavior()
        {
            ScanTarget();
            UpdateStates();
            movement?.UpdateMovement();
        }

        protected override void OnSpawned()
        {
            InitializeFeatures();
        }

        protected override void InitializeSpawnData()
        {
            if (levelModel.Level.monsters != null && monster.MonsterDataIndex < levelModel.Level.monsters.Count)
            {
                var monsterSpawnData = levelModel.Level.monsters[monster.MonsterDataIndex];

                // Set patrol area for movement
                if (monsterSpawnData.HasPatrolArea)
                {
                    movement?.SetPatrolArea(monsterSpawnData.patrolCenter, monsterSpawnData.patrolArea);
                }

                if (HasStateAuthority)
                {
                    movement?.TeleportToPosition(monsterSpawnData.spawn);
                }
            }
        }

        private void InitializeFeatures()
        {
            // Initialize movement based on type
            movement = movementType switch
            {
                MovementType.NavMesh => navMeshMovement,
                MovementType.Jumping => jumpingMovement,
                _ => navMeshMovement
            };
            movement?.Initialize(monster, monster.transform);

            // Initialize attack based on type
            attack = attackType switch
            {
                AttackType.Basic => basicAttack,
                AttackType.Delayed => delayedAttack,
                _ => basicAttack
            };

            // Configure attack based on concrete type
            switch (attack)
            {
                case BasicAttack basic:
                    basic.SetConfig(monstersConfig);
                    break;
                case DelayedAttack delayed:
                    delayed.SetConfig(monstersConfig, effectsManager, destroyCancellationToken);
                    break;
            }

            attack?.Initialize();
        }

        private void UpdateStates()
        {
            if (!HasTarget)
            {
                HandleNoTargetState();
            }
            else
            {
                HandleTargetState();
            }

            HandleTransitions();
        }

        private void HandleNoTargetState()
        {
            if (useIdlePatrolSwitching)
            {
                if (Time.time >= nextIdleOrWalkSwitchTime)
                {
                    nextIdleOrWalkSwitchTime = Time.time + Random.Range(1f, 15f);
                    State = State == MonsterState.Idle ? MonsterState.Patrol : MonsterState.Idle;
                }

                if (State == MonsterState.Patrol)
                {
                    movement?.Patrol(PatrolSpeed);
                }
                else if (State == MonsterState.Idle)
                {
                    movement?.Stop();
                }
            }
            else
            {
                State = MonsterState.Patrol;
                movement?.Patrol(PatrolSpeed);
            }
        }

        private void HandleTargetState()
        {
            if (IsInRange())
            {
                State = MonsterState.Attack;
                if (attachToPlayerOnAttack)
                {
                    AttachToPlayer();
                }
                Attack();
            }
            else
            {
                State = MonsterState.Chase;
                movement?.Chase(targetPlayer.transform.position, ChaseSpeed);
            }

            if (useIdlePatrolSwitching)
            {
                nextIdleOrWalkSwitchTime = Time.time;
            }
        }

        private void HandleTransitions()
        {
            if (disableNavMeshOnAttack)
            {
                var navMesh = movement as NavMeshMovement;
                if (navMesh != null)
                {
                    // Handle NavMesh enable/disable for attack transitions
                    if (previousFrameState == MonsterState.Attack && State != MonsterState.Attack)
                    {
                        navMesh.EnableNavMesh(true);
                        if (State != MonsterState.DamageReceive)
                        {
                            nextAttackTime = 0;
                        }
                    }

                    if (previousFrameState != MonsterState.Attack && State == MonsterState.Attack)
                    {
                        navMesh.EnableNavMesh(false);
                        if (previousFrameState != MonsterState.DamageReceive)
                        {
                            nextAttackTime = 0;
                            SetAttackAudioRpc();
                        }
                    }
                }
            }

            // Handle chase sound transitions
            if (previousFrameState != MonsterState.Chase && State == MonsterState.Chase)
            {
                SetScreamRpc(true);
            }

            if (previousFrameState == MonsterState.Chase && State != MonsterState.Chase)
            {
                SetScreamRpc(false);
            }
        }

        private void AttachToPlayer()
        {
            var targetPosition = targetPlayer.transform.position + targetPlayer.transform.forward * 2f;
            targetPosition.y -= 1.5f;
            monster.transform.position = targetPosition;
            monster.transform.rotation = Quaternion.LookRotation(-targetPlayer.transform.forward.OnlyXZ().normalized, Vector3.up);
        }

        private void Attack()
        {
            if (nextAttackTime == 0)
            {
                nextAttackTime = Time.time + monstersConfig.InitialAttackDelay;
                return;
            }

            if (!attack.CanAttack(Time.time, nextAttackTime))
            {
                return;
            }

            attack.OnAttackStart();
            attack.Attack(targetPlayer, AttackDamage);
            nextAttackTime = attack.GetNextAttackTime(Time.time);
            attack.OnAttackEnd();
        }

        private void ScanTarget()
        {
            if (targetPlayer != null)
            {
                // First try to switch targets based on distance
                if (Time.time >= nextTargetSwitchTime && playersModel.TryFindClosestPlayer(monster.transform.position, monstersConfig.ScanPlayerRadius, IsPlayerTargetable, out var player, 3))
                {
                    nextTargetSwitchTime = Time.time + targetSwitchInterval;
                    SetNewTargetRpc(player);
                    return;
                }

                // If could not switch targets, check if current target is still valid
                if (!IsPlayerTargetable(targetPlayer) || Vector3.Distance(targetPlayer.transform.position, monster.transform.position) > monstersConfig.ScanPlayerRadius)
                {
                    SetNewTargetRpc(null);
                }
            }
            else
            {
                // Always scan for new target if target is null
                if (playersModel.TryFindClosestPlayer(monster.transform.position, monstersConfig.ScanPlayerRadius, IsPlayerTargetable, out var player, 3))
                {
                    if (targetPlayer != player)
                    {
                        SetNewTargetRpc(player);
                    }
                }
            }
        }

        protected override async UniTaskVoid TransferAuthority()
        {
            // Let movement prepare for transfer
            movement?.PrepareForAuthorityTransfer();

            // Handle the actual networking
            var result = await Object.RequestStateAuthorityAsync(DespawnCancellationToken);

            if (result)
            {
                nextTargetSwitchTime = Time.time + targetSwitchInterval;

                // Let movement handle post-transfer setup
                movement?.OnAuthorityTransferred();
            }
        }

        protected override bool IsPlayerTargetable(PlayerActor player)
        {
            if (!base.IsPlayerTargetable(player))
            {
                return false;
            }

            // Check patrol area if using NavMesh movement
            var navMesh = movement as NavMeshMovement;
            if (navMesh != null && !navMesh.IsTargetInPatrolArea(player.transform.position))
            {
                return false;
            }

            return true;
        }

        protected override void ReceiveDamage()
        {
            if (IsDamageReceiving)
            {
                return;
            }

            if (disableNavMeshOnAttack)
            {
                State = MonsterState.DamageReceive;
            }
            
            nextDamageReceiveTime = Time.time + 0.25f;
        }
    }
}
