using System;
using Cysharp.Threading.Tasks;
using Game.Views.Monsters;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Game.Controllers.Monsters.Features
{
    [Serializable]
    public class PhysicsMovement : IMonsterMovement
    {
        [SerializeField] private Rigidbody rootRigidbody;
        
        private MonsterActor monster;
        private Transform monsterTransform;
        private VoxelSpaceManager voxelSpaceManager;
        
        private Vector3 Forward => monsterTransform.forward;
        private Vector3 Up => monsterTransform.up;
        private Vector3 Down => -Up;
        
        private float nextRotationTime;
        private float nextJumpTime;
        private bool CanRandomRotate => Time.time > nextRotationTime;

        public void Initialize(MonsterActor monster, Transform transform)
        {
            this.monster = monster;
            this.monsterTransform = transform;
        }

        public void SetVoxelSpaceManager(VoxelSpaceManager voxelManager)
        {
            voxelSpaceManager = voxelManager;
        }

        public void UpdateMovement()
        {
            // Physics movement doesn't need constant updates like NavMesh
        }

        public void SetPatrolArea(Vector3 center, Vector3 area)
        {
            // Physics movement doesn't use patrol areas
        }

        public void Patrol(float speed)
        {
            if (CanRandomRotate)
            {
                SetRandomRotation();
                nextRotationTime = Time.time + Random.Range(1f, 10f);
            }

            UpdateOrientation();
            MoveForward(speed);
        }

        public void Chase(Vector3 targetPosition, float speed)
        {
            if (Time.time > nextJumpTime && rootRigidbody.linearVelocity.magnitude < 0.1f)
            {
                ApplyJumpForce(targetPosition, null);
            }
        }

        public void Stop()
        {
            rootRigidbody.linearVelocity = Vector3.zero;
        }

        public async UniTaskVoid TransferAuthority()
        {
            await monster.Object.RequestStateAuthorityAsync(monster.DespawnCancellationToken);
            nextJumpTime = Time.time + 1f;
        }

        public bool IsValidPosition()
        {
            return true; // Physics movement is always valid
        }

        public void TeleportToPosition(Vector3 position)
        {
            monsterTransform.position = position;
        }

        public void EnablePhysics(bool enabled)
        {
            if (enabled)
            {
                RotateToUpright();
                rootRigidbody.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
                rootRigidbody.useGravity = true;
                rootRigidbody.isKinematic = false;
            }
            else
            {
                rootRigidbody.constraints = RigidbodyConstraints.FreezeAll;
                rootRigidbody.useGravity = false;
                rootRigidbody.isKinematic = true;
            }
        }

        public void HandleCollision(Vector3 collisionNormal, Vector3 targetPosition)
        {
            ApplyJumpForce(targetPosition, collisionNormal);
        }

        private void SetRandomRotation()
        {
            monsterTransform.rotation *= Quaternion.AngleAxis(Random.Range(-180f, 180f), Vector3.up);
        }

        private void UpdateOrientation()
        {
            if (UsingPhysics())
            {
                return;
            }

            if (TryDetectOrientation(out var point, out var normal))
            {
                SetOrientation(point, normal);
            }
        }

        private void MoveForward(float speed)
        {
            if (UsingPhysics()) return;
            monsterTransform.position += speed * Time.fixedDeltaTime * Forward;
        }

        private void ApplyJumpForce(Vector3 targetPosition, Vector3? collisionNormal)
        {
            var directionToTarget = (targetPosition - monsterTransform.position).normalized;
            var jumpForce = monster.ChaseSpeed;
            var jumpDirection = Vector3.zero;

            var randomX = Random.Range(-0.5f, 0.5f);
            var randomY = Random.Range(3f, 6f);
            var randomZ = Random.Range(-0.5f, 0.5f);
            var randomDirection = new Vector3(randomX, randomY, randomZ);

            if (collisionNormal.HasValue)
            {
                var normal = collisionNormal.Value;

                if (normal.y < -0.7f)
                {
                    // Hit roof - jump straight to player
                    jumpDirection = directionToTarget.normalized;
                }
                else if (Mathf.Abs(normal.y) < 0.3f)
                {
                    // Hit wall - check if there's obstacle between spider and player
                    if (IsObstacleBetweenSpiderAndPlayer(targetPosition))
                    {
                        // Obstacle detected - jump straight up
                        jumpDirection = Vector3.up;
                        jumpForce = monster.ChaseSpeed * 1.2f;
                    }
                    else
                    {
                        // No obstacle - jump straight to player
                        jumpDirection = directionToTarget.normalized;
                    }
                }
                else
                {
                    // Hit ground - jump towards player with up component
                    jumpDirection = (directionToTarget + Vector3.up * 2f + randomDirection).normalized;
                }
            }
            else
            {
                jumpDirection = (directionToTarget + randomDirection).normalized;
            }

            RotateToJumpDirection(jumpDirection);

            var currentVelocity = rootRigidbody.linearVelocity;
            var currentHorizontalVelocity = new Vector3(currentVelocity.x, 0, currentVelocity.z);
            var newHorizontalDirection = new Vector3(jumpDirection.x, 0, jumpDirection.z).normalized;

            if (currentHorizontalVelocity.magnitude > 0.1f)
            {
                var currentHorizontalDirection = currentHorizontalVelocity.normalized;
                var angle = Vector3.Angle(currentHorizontalDirection, newHorizontalDirection);

                if (angle > 45f)
                {
                    rootRigidbody.linearVelocity = Vector3.zero;
                }
            }
            else
            {
                rootRigidbody.linearVelocity = Vector3.zero;
            }

            nextJumpTime = Time.time + 1f;
            rootRigidbody.AddForce(jumpDirection * jumpForce, ForceMode.Acceleration);
        }

        private bool IsObstacleBetweenSpiderAndPlayer(Vector3 targetPosition)
        {
            var spiderPosition = monsterTransform.position;
            var directionToPlayer = (targetPosition - spiderPosition).normalized;
            var distanceToPlayer = Vector3.Distance(spiderPosition, targetPosition);

            // Raycast from spider to player to check for obstacles
            return Physics.Raycast(spiderPosition, directionToPlayer, distanceToPlayer - 0.5f, 1 << Layers.Default);
        }

        private void RotateToJumpDirection(Vector3 jumpDirection)
        {
            var horizontalDirection = new Vector3(jumpDirection.x, 0, jumpDirection.z).normalized;

            if (horizontalDirection.sqrMagnitude > 0.1f)
            {
                var targetRotation = Quaternion.LookRotation(horizontalDirection, Vector3.up);
                monsterTransform.rotation = targetRotation;
            }
        }

        private void RotateToUpright()
        {
            var targetRotation = Quaternion.FromToRotation(monsterTransform.up, Vector3.up) * monsterTransform.rotation;
            monsterTransform.rotation = targetRotation;
        }

        private bool UsingPhysics()
        {
            return !rootRigidbody.isKinematic && rootRigidbody.constraints != RigidbodyConstraints.FreezeAll;
        }

        private bool TryDetectOrientation(out Vector3 point, out Vector3 normal)
        {
            const float forwardOffset = 0.5f;
            const float downOffset = 0.25f;
            const float upOffset = 0.5f;
            const float backOffset = 10f;

            if (TryRaycast(monsterTransform.position + upOffset * Up, Forward, forwardOffset, out var hitWall))
            {
                point = hitWall.point;
                normal = hitWall.normal;
                return true;
            }

            if (!voxelSpaceManager.CheckCollision(monsterTransform.position + downOffset * Forward + downOffset * Down))
            {
                if (TryRaycast(monsterTransform.position + forwardOffset * Forward + downOffset * Down, -Forward, backOffset, out var hit))
                {
                    point = hit.point;
                    normal = hit.normal;
                    return true;
                }
            }

            point = normal = Vector3.zero;
            return false;
        }

        private void SetOrientation(Vector3 point, Vector3 normal)
        {
            var localRotation = Quaternion.Inverse(monsterTransform.rotation);
            var angle = -localRotation.eulerAngles.y;

            monsterTransform.position = point;
            monsterTransform.up = normal;
            monsterTransform.rotation *= Quaternion.AngleAxis(angle, Vector3.up);
        }

        private bool TryRaycast(Vector3 point, Vector3 direction, float maxDistance, out RaycastHit hit)
        {
            return Physics.Raycast(point, direction, out hit, maxDistance, 1 << Layers.Default);
        }

        public bool TryApplyGravity()
        {
            if (UsingPhysics()) return false;
            if (TryRaycast(monsterTransform.position + 0.2f * Up, Down, 50, out var hit) && hit.distance > 0.5f)
            {
                monster.Teleport(hit.point);
                return true;
            }

            return false;
        }
    }
}
