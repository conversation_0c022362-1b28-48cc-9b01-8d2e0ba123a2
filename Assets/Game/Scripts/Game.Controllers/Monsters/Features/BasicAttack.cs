using System;
using Game.Views.Monsters;
using Game.Views.Players;

namespace Game.Controllers.Monsters.Features
{
    [Serializable]
    public class BasicAttack : IMonsterAttack
    {
        private MonstersConfig monstersConfig;

        public void Initialize()
        {
        }

        public void SetConfig(MonstersConfig config)
        {
            monstersConfig = config;
        }

        public void Attack(PlayerActor target, byte damage)
        {
            target.SetDamageByMonsterRpc(damage);
        }

        public bool CanAttack(float currentTime, float lastAttackTime)
        {
            return currentTime >= lastAttackTime;
        }

        public float GetNextAttackTime(float currentTime)
        {
            return currentTime + monstersConfig.AttackInterval;
        }

        public void OnAttackStart()
        {
        }

        public void OnAttackEnd()
        {
        }
    }
}
