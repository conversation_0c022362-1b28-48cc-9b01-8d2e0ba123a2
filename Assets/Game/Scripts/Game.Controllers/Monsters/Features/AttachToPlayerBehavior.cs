using System;
using Game.Views.Players;
using Modules.Core;
using UnityEngine;

namespace Game.Controllers.Monsters.Features
{
    [Serializable]
    public class AttachToPlayerBehavior : IMonsterBehavior
    {
        private Transform monsterTransform;

        public bool RequiresMovementDisable => true;

        public void Initialize()
        {
        }

        public void SetMonsterTransform(Transform transform)
        {
            monsterTransform = transform;
        }

        public void OnAttackStart(PlayerActor target)
        {
            if (target == null || monsterTransform == null) return;

            var targetPosition = target.transform.position + target.transform.forward * 2f;
            targetPosition.y -= 1.5f;
            monsterTransform.position = targetPosition;
            monsterTransform.rotation = Quaternion.LookRotation(-target.transform.forward.OnlyXZ().normalized, Vector3.up);
        }

        public void OnAttackEnd()
        {
        }
    }
}
