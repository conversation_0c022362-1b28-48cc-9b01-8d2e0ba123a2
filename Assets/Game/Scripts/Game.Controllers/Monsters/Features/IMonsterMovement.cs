using UnityEngine;

namespace Game.Controllers.Monsters.Features
{
    public interface IMonsterMovement
    {
        void Initialize(MonsterActor monster, Transform transform);
        void UpdateMovement();
        void SetPatrolArea(Vector3 center, Vector3 area);
        void Patrol(float speed);
        void Chase(Vector3 targetPosition, float speed);
        void Stop();
        bool IsValidPosition();
        void TeleportToPosition(Vector3 position);
        void PrepareForAuthorityTransfer();
        void OnAuthorityTransferred();
    }
}
