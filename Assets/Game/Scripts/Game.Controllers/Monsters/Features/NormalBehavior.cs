using System;
using Game.Views.Players;

namespace Game.Controllers.Monsters.Features
{
    [Serializable]
    public class NormalBehavior : IMonsterBehavior
    {
        public bool RequiresMovementDisable => false;

        public void Initialize()
        {
        }

        public void OnAttackStart(PlayerActor target)
        {
        }

        public void OnAttackEnd()
        {
        }
    }
}
