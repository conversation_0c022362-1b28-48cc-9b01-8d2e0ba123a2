using System;
using Cysharp.Threading.Tasks;
using Game.Views.Monsters;
using Game.Views.PlayerEffects;
using Game.Views.Players;
using UnityEngine;

namespace Game.Controllers.Monsters.Features
{
    [Serializable]
    public class DelayedAttack : IMonsterAttack
    {
        [SerializeField] private float attackDelay = 2f;
        
        private MonstersConfig monstersConfig;
        private PlayerEffectsManager effectsManager;
        private System.Threading.CancellationToken cancellationToken;

        public void Initialize()
        {
        }

        public void SetConfig(MonstersConfig config, PlayerEffectsManager effects, System.Threading.CancellationToken token)
        {
            monstersConfig = config;
            effectsManager = effects;
            cancellationToken = token;
        }

        public void Attack(PlayerActor target, byte damage)
        {
            AttackDelayed(target, damage).Forget();
        }

        public bool CanAttack(float currentTime, float lastAttackTime)
        {
            return currentTime >= lastAttackTime;
        }

        public float GetNextAttackTime(float currentTime)
        {
            return currentTime + monstersConfig.AttackInterval;
        }

        public void OnAttackStart()
        {
            effectsManager?.RenderSpiderBossDamageEffect(1f);
        }

        public void OnAttackEnd()
        {
        }

        private async UniTaskVoid AttackDelayed(PlayerActor target, byte damage)
        {
            await UniTask.WaitForSeconds(attackDelay, cancellationToken: cancellationToken);
            if (target == null) return;
            
            target.SetDamageByMonsterRpc(damage);
        }
    }
}
