using System;
using Game.Views.Monsters;
using UnityEngine;
using UnityEngine.AI;
using Random = UnityEngine.Random;

namespace Game.Controllers.Monsters.Features
{
    [Serializable]
    public class NavMeshMovement : IMonsterMovement
    {
        [SerializeField] private NavMeshAgent navMeshAgent;

        private MonsterActor monster;
        private Transform monsterTransform;
        private float nextPathUpdateTime;
        private const float pathUpdateInterval = 0.2f;

        private float patrolBoundsXMin, patrolBoundsXMax, patrolBoundsZMin, patrolBoundsZMax, patrolBoundsYMin, patrolBoundsYMax;
        private bool hasPatrolArea;

        public void Initialize(MonsterActor monster, Transform transform)
        {
            this.monster = monster;
            monsterTransform = transform;
        }

        public void UpdateMovement()
        {
            TryPlaceOnNavMesh();
        }

        public void SetPatrolArea(Vector3 center, Vector3 area)
        {
            hasPatrolArea = area != Vector3.zero;
            if (hasPatrolArea)
            {
                patrolBoundsXMin = center.x - 0.5f * Math.Abs(area.x);
                patrolBoundsXMax = center.x + 0.5f * Math.Abs(area.x);
                patrolBoundsZMin = center.z - 0.5f * Math.Abs(area.z);
                patrolBoundsZMax = center.z + 0.5f * Math.Abs(area.z);
                patrolBoundsYMin = center.y - 0.5f * Math.Abs(area.y);
                patrolBoundsYMax = center.y + 0.5f * Math.Abs(area.y);
            }
        }

        public void Patrol(float speed)
        {
            if (!navMeshAgent.isOnNavMesh) return;

            navMeshAgent.speed = speed;
            if (navMeshAgent.pathStatus == NavMeshPathStatus.PathPartial || navMeshAgent.remainingDistance < 0.5f)
            {
                navMeshAgent.SetDestination(GetRandomNavMeshPoint());
            }
        }

        public void Chase(Vector3 targetPosition, float speed)
        {
            if (!navMeshAgent.isOnNavMesh) return;

            navMeshAgent.speed = speed;
            if (Time.time >= nextPathUpdateTime)
            {
                navMeshAgent.SetDestination(targetPosition);
                nextPathUpdateTime = Time.time + pathUpdateInterval;
            }
        }

        public void Stop()
        {
            if (navMeshAgent.isOnNavMesh)
            {
                navMeshAgent.ResetPath();
            }
        }

        public void PrepareForAuthorityTransfer()
        {
            navMeshAgent.enabled = false;
            navMeshAgent.updateRotation = false;
        }

        public void OnAuthorityTransferred()
        {
            monster.Teleport(monsterTransform.position);
            navMeshAgent.enabled = true;
            navMeshAgent.updateRotation = true;
        }

        public bool IsValidPosition()
        {
            return navMeshAgent.isOnNavMesh;
        }

        public void TeleportToPosition(Vector3 position)
        {
            navMeshAgent.enabled = false;
            monsterTransform.position = position;
            navMeshAgent.enabled = true;
        }

        public bool IsTargetInPatrolArea(Vector3 targetPosition)
        {
            if (!hasPatrolArea) return true;

            return targetPosition.x >= patrolBoundsXMin &&
                   targetPosition.x <= patrolBoundsXMax &&
                   targetPosition.z >= patrolBoundsZMin &&
                   targetPosition.z <= patrolBoundsZMax &&
                   targetPosition.y >= patrolBoundsYMin &&
                   targetPosition.y <= patrolBoundsYMax;
        }

        public void EnableNavMesh(bool enabled)
        {
            navMeshAgent.enabled = enabled;
        }

        private void TryPlaceOnNavMesh()
        {
            if (!navMeshAgent.enabled || navMeshAgent.isOnNavMesh) return;

            if (NavMesh.SamplePosition(monsterTransform.position, out var hit, 10f, NavMesh.AllAreas))
            {
                var newPosition = new Vector3(monsterTransform.position.x, hit.position.y, monsterTransform.position.z);
                monsterTransform.position = newPosition;
            }
        }

        private Vector3 GetRandomNavMeshPoint()
        {
            Vector3 searchOrigin;
            float searchRadius;

            if (hasPatrolArea)
            {
                searchRadius = 1f;
                searchOrigin = new Vector3(
                    Random.Range(patrolBoundsXMin, patrolBoundsXMax),
                    monsterTransform.position.y,
                    Random.Range(patrolBoundsZMin, patrolBoundsZMax)
                );

                if (NavMesh.SamplePosition(searchOrigin, out var hit, searchRadius, NavMesh.AllAreas))
                {
                    return hit.position;
                }
            }
            else
            {
                searchOrigin = monsterTransform.position;
                searchRadius = 10f;
                var randomDirection = Random.insideUnitSphere * 10f;
                randomDirection += searchOrigin;

                if (NavMesh.SamplePosition(randomDirection, out var hit, searchRadius, NavMesh.AllAreas))
                {
                    return hit.position;
                }
            }

            return monsterTransform.position;
        }
    }
}
