using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.Levels;
using Game.Views.UI.Screens.Menu;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Screens
{
    public class MenuPanelController : ControllerBase
    {
        private MenuScreen menuScreen;
        private MenuPanel menuPanel;
        private LevelModel levelModel;
        private GameModel gameModel;

        [Inject]
        private void Construct(
            IScreenManager screenManager,
            INetworkClient networkClient,
            LevelModel levelModel,
            EconomyModel economyModel,
            AppConfig appConfig,
            GameModel gameModel)
        {
            this.levelModel = levelModel;
            this.gameModel = gameModel;
            
            menuScreen = screenManager.GetScreen<MenuScreen>(true);
            menuPanel = menuScreen.GetPanel<MenuPanel>();

            menuPanel.SetAppVersion(appConfig.GameVersion);

            menuPanel.OnOpenModeration.Subscribe(_ => menuScreen.OpenPanel<ModerationPanel>()).AddTo(DisposeCancellationToken);

            networkClient.PlayerCount.Subscribe(playerCount => menuPanel.SetPlayerCount($"{playerCount}")).AddTo(DisposeCancellationToken);
            economyModel.CoinAmount.Subscribe(remzBalance => menuPanel.SetRemzBalance($"{remzBalance} <size=14>{Constants.CoinName}</size>")).AddTo(DisposeCancellationToken);
            levelModel.OnLevelLoaded.Subscribe(HandleLevelLoaded).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded(bool isOk)
        {
            if (!isOk)
            {
                return;
            }

            var levelData = levelModel.Level;
            menuPanel.SetSceneName(levelData.name);
            menuPanel.SetAdminSettingsWidgetActive(gameModel.IsAdmin);
        }
    }
}