using System.Collections.Generic;

namespace Game.Core.Data
{
    public readonly struct PurchaseResultData
    {
        public readonly int rewardCoinAmount;
        public readonly List<ShopInventoryData> rewardInventoryList;

        public PurchaseResultData(int rewardCoinAmount, List<ShopInventoryData> rewardInventoryList)
        {
            this.rewardCoinAmount = rewardCoinAmount;
            this.rewardInventoryList = rewardInventoryList;
        }

        public string GetInventoryTitles()
        {
            var titles = string.Empty;

            for (var i = 0; i < rewardInventoryList.Count; i++)
            {
                var inventory = rewardInventoryList[i];

                if (i > 0)
                {
                    titles += ", ";
                }

                titles += inventory.title;
            }

            return titles;
        }
    }
}