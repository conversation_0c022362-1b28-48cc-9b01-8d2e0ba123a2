using Game.Views.Consumeables;
using Game.Views.Grabbing;
using Game.Views.Guns;
using Game.Views.InGameRewards;
using Game.Views.Monsters;
using Game.Views.Players;
using Game.Views.Weapons;
using Game.Views.Wings;
using Game.Views.Zombies;
using MessagePipe;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Main
{
    public class MessagePipeMonoInstaller : MonoInstaller
    {
        public override void Install(IContainerBuilder builder, Transform node = null)
        {
            var options = builder.RegisterMessagePipe();
            builder.RegisterBuildCallback(c => GlobalMessagePipe.SetProvider(c.AsServiceProvider()));

            // Messages
            builder.RegisterMessageBroker<AvatarChangeArgs>(options);
            builder.RegisterMessageBroker<WingsUpdateArgs>(options);
            builder.RegisterMessageBroker<WeaponDamageArgs>(options);
            builder.RegisterMessageBroker<GrabbableItemGrabArgs>(options);
            builder.RegisterMessageBroker<ConsumableConsumeArgs>(options);
            builder.RegisterMessageBroker<ZombieDamageArgs>(options);
            builder.RegisterMessageBroker<ZombieKillArgs>(options);
            builder.RegisterMessageBroker<MonsterDamageArgs>(options);
            builder.RegisterMessageBroker<MonsterKillArgs>(options);
            builder.RegisterMessageBroker<RaycastGunDamageArgs>(options);
            builder.RegisterMessageBroker<RewardArgs>(options);
        }
    }
}