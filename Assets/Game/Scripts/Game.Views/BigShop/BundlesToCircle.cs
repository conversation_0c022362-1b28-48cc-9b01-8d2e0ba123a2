using System.Collections.Generic;
using Modules.Core;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Views.BigShop
{
    public class BundlesToCircle : MonoBehaviour
    {
        [SerializeField] private float radius;
        [SerializeField] private List<Transform> nodeList;

        [Button]
        private void SetBundlesToCircle()
        {
            for (var i = 0; i < nodeList.Count; ++i)
            {
                var node = nodeList[i];
                var circlePosition = (float)i / nodeList.Count;
                var x = Mathf.Sin(circlePosition * Mathf.PI * 2) * radius;
                var z = Mathf.Cos(circlePosition * Mathf.PI * 2) * radius;
                node.position = new Vector3(x, node.position.y, z);
                node.rotation = Quaternion.LookRotation((node.position - transform.position).OnlyXZ());
            }
        }
    }
}