using Game.Views.Moderation;
using TMPro;
using UnityEngine;

namespace Game.Views.Weapons
{
    public class ModerationStickView : WeaponView
    {
        [SerializeField] private TMP_Text sentenceText, sentenceText2;
        [SerializeField] private GameObject sentenceNode;

        private void OnDisable()
        {
            SetActiveSentence(ModerationSentence.None);
        }

        public void SetActiveSentence(ModerationSentence sentence)
        {
            if (sentence == ModerationSentence.None)
            {
                sentenceNode.SetActive(false);
            }
            else
            {
                sentenceText.text = sentenceText2.text = sentence.ToString();
                sentenceNode.SetActive(true);
            }
        }
    }
}