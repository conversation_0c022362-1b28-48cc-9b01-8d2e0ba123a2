using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Fusion;
using Game.Core;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Views.Zombies
{
    public class ZombieActor : NetworkActor
    {
        [Header("Zombie")]
        [SerializeField] private Rigidbody selfRigidbody;
        [SerializeField] private Collider selfCollider;
        [SerializeField] private NetworkTransform networkTransform;
        [SerializeField] private Transform avatarNode;
        [SerializeField] private Transform audioNode;

        private IPublisher<ZombieDamageArgs> damagePublisher;
        private IPublisher<ZombieKillArgs> killPublisher;
        private ZombiesManager zombiesManager;
        private PlayersModel playersModel;
        private IAudioClient audioClient;
        private ZombieAvatar avatar;
        private bool isKilled;

        public bool IsAlive => Health > 0;
        public Vector3 Center => avatar == null ? Vector3.zero : avatar.Center;

        [Networked] [OnChangedRender(nameof(ChangeAvatarId))]
        public byte AvatarId { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeHealth))]
        public byte Health { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeZombieState))]
        public ZombieState State { get; set; }

        [Inject]
        private void Construct(
            IAudioClient audioClient,
            ZombiesManager zombiesManager,
            PlayersModel playersModel,
            IPublisher<ZombieKillArgs> killPublisher,
            IPublisher<ZombieDamageArgs> damagePublisher)
        {
            this.damagePublisher = damagePublisher;
            this.zombiesManager = zombiesManager;
            this.playersModel = playersModel;
            this.killPublisher = killPublisher;
            this.audioClient = audioClient;
        }

        public override void Spawned()
        {
            base.Spawned();
            ChangeAvatarId();
            ChangeZombieState();
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            zombiesManager.DestroyAvatar(avatar);
            avatar = null;
        }

        public void Teleport(Vector3 position)
        {
            transform.position = position;
            networkTransform.Teleport(position, transform.rotation);
        }

        public void PlayAudio(string audioKey)
        {
            audioClient.Play(audioKey, audioNode, destroyCancellationToken);
        }

        public bool IsKillable(int damage)
        {
            return Health - damage <= 0;
        }

        public void Damage(byte damage, Vector3 damageDirection)
        {
            SendRpcSafe(() => DamageRpc(damage, damageDirection));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void DamageRpc(byte damage, Vector3 damageDirection, RpcInfo info = default)
        {
            if (isKilled)
            {
                return;
            }

            var isAlive = Health - damage > 0;

            if (HasStateAuthority)
            {
                Health = (byte)Mathf.Max(0, Health - damage);
            }

            if (isAlive)
            {
                SetDamage(damage);
            }
            else
            {
                SetKill(damageDirection, info.Source);
            }
        }

        private void SetDamage(byte damage)
        {
            if (!HasStateAuthority)
            {
                return;
            }

            damagePublisher.Publish(new ZombieDamageArgs(this, damage));
        }

        private void SetKill(Vector3 damageDirection, PlayerRef killer)
        {
            if (avatar != null)
            {
                avatar.SetActive(false);
            }

            if (IsInRange())
            {
                CreateRagdollAndAddForce(damageDirection);
            }

            if (HasStateAuthority)
            {
                killPublisher.Publish(new ZombieKillArgs(killer));
                UniTaskAsyncEnumerable.Timer(TimeSpan.FromSeconds(1)).Subscribe(_ => zombiesManager.DestroyZombie(this)).AddTo(destroyCancellationToken);
            }

            isKilled = true;
        }

        private void CreateRagdollAndAddForce(Vector3 force)
        {
            if (avatar == null)
            {
                return;
            }

            var ragdoll = zombiesManager.CreateRagdoll(avatar.Code);
            ragdoll.transform.SetPositionAndRotation(transform.position, transform.rotation);
            ragdoll.SetPose(avatar.GetNodeList());
            ragdoll.AddForce(force);
        }

        private void ChangeAvatarId()
        {
            zombiesManager.DestroyAvatar(avatar);
            avatar = zombiesManager.CreateAvatar(AvatarId, avatarNode);
        }

        private void ChangeHealth(NetworkBehaviourBuffer previous)
        {
            if (IsInRange() && IsAlive && Health < GetPropertyReader<byte>(nameof(Health)).Read(previous))
            {
                audioClient.Play(AudioKeys.ZombiePain, audioNode.position, destroyCancellationToken);
            }
        }

        private void ChangeZombieState()
        {
            if (avatar == null)
            {
                return;
            }

            switch (State)
            {
                case ZombieState.Idle:
                    avatar.PlayIdleAnimation();
                    break;
                case ZombieState.Patrol:
                    avatar.PlayWalkAnimation();
                    break;
                case ZombieState.Chase:
                    avatar.PlayRunAnimation();
                    break;
                case ZombieState.Attack:
                    avatar.PlayAttackAnimation();
                    break;
                case ZombieState.DamageReceive:
                    avatar.PlayDamageReceiveAnimation();
                    break;
            }
        }

        private bool IsInRange()
        {
            return playersModel.IsLocalPlayerInRadius(transform.position, 5);
        }
    }
}