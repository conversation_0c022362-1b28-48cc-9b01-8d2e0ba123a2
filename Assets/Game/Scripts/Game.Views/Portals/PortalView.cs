using System;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.Views.Players;
using Game.Views.Shared;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Portals
{
    public abstract class PortalView : Actor
    {
        [SerializeField] protected XRPlayerTrigger playerTrigger;
        [SerializeField] protected GameObject viewObject;

        private readonly ISubject<PortalView> onPlayerTriggered = new Subject<PortalView>();

        protected LevelMetaData levelData;
        private DistanceCuller distanceCuller;
        private CancellationTokenSource disableCancellationTokenSource;

        public bool HasLevelData => levelData != null;
        public string LevelId => HasLevelData ? levelData.id : string.Empty;
        public string LevelPassword { get; private set; }
        public IObservable<PortalView> OnPlayerTriggered => onPlayerTriggered;
        public CancellationToken DisableCancellationToken
        {
            get
            {
                disableCancellationTokenSource ??= new CancellationTokenSource();
                return disableCancellationTokenSource.Token;
            }
        }

        [Inject]
        private void Construct(DistanceCuller distanceCuller)
        {
            this.distanceCuller = distanceCuller;
        }

        protected virtual void OnEnable()
        {
            playerTrigger.OnEntered.Subscribe(_ => HandlePlayerTriggered()).AddTo(DisableCancellationToken);
            distanceCuller.AddTarget(viewObject, 50);
        }

        protected virtual void OnDisable()
        {
            if (disableCancellationTokenSource != null)
            {
                disableCancellationTokenSource.CancelAndDispose();
                disableCancellationTokenSource = null;
            }

            distanceCuller.RemoveTarget(viewObject);
        }

        protected virtual void HandlePlayerTriggered()
        {
            onPlayerTriggered.OnNext(this);
        }

        public virtual void Initialize(LevelMetaData levelData, string levelPassword = null)
        {
            LevelPassword = levelPassword;
        }

        public void SetActiveTrigger(bool isActive)
        {
            playerTrigger.SetActive(isActive);
        }
    }
}