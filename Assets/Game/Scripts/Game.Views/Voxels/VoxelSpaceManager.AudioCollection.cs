using Modules.Core;
using UnityEngine;

namespace Game.Views.Voxels
{
    public partial class VoxelSpaceManager
    {
        private static AudioCollection voxelAudioCollection;

        public AudioCollection GetVoxelAudioCollection()
        {
            if (voxelAudioCollection == null)
            {
                voxelAudioCollection = CreateVoxelAudioCollection();
            }

            return voxelAudioCollection;
        }

        private AudioCollection CreateVoxelAudioCollection()
        {
            var audioCollection = ScriptableObject.CreateInstance<AudioCollection>();
            audioCollection.name = "VoxelsAudioCollection";

            foreach (var voxel in voxelConfig.AllVoxelDefList)
            {
                if (voxel == null)
                {
                    continue;
                }

                var footfalls = voxel.footfalls;
                if (footfalls != null)
                {
                    foreach (var footfall in footfalls)
                    {
                        if (footfall == null)
                        {
                            continue;
                        }

                        audioCollection.AddAudioData(CreateAudioData(footfall.name, footfall));
                    }
                }

                var pickup = voxel.pickupSound;
                if (pickup != null)
                {
                    if (pickup == null)
                    {
                        continue;
                    }

                    audioCollection.AddAudioData(CreateAudioData(pickup.name, pickup));
                }
            }

            return audioCollection;
        }

        private AudioData CreateAudioData(string key, AudioClip clip)
        {
            return new AudioData
            {
                key = key,
                clip = clip,
                group = AudioMixerGroup.Sfx,
                isSpatial = true,
                loop = false,
                volume = 0.1f,
                maxDistance = 30,
                minDistance = 0.2f,
                rolloffMode = AudioRolloffMode.Logarithmic
            };
        }
    }
}