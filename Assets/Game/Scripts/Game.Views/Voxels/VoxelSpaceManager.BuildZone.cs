using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using UnityEngine;

namespace Game.Views.Voxels
{
    public partial class VoxelSpaceManager
    {
        public BuildIslandMap GetBuildIslandMap(Bounds bounds, Pose originPose)
        {
            const float shift = 0.5f;

            var voxelList = new List<BuildIslandVoxel>();
            var min = bounds.min;
            var max = bounds.max;
            var originPoint = new Vector3(GetSnapValue(originPose.position.x), GetSnapValue(originPose.position.y), GetSnapValue(originPose.position.z));
            var originRotation = Quaternion.Inverse(originPose.rotation);

            for (var x = min.x + shift; x < max.x; x++)
            {
                for (var y = min.y + shift; y < max.y; y++)
                {
                    for (var z = min.z + shift; z < max.z; z++)
                    {
                        var point = new Vector3(GetSnapValue(x), GetSnapValue(y), GetSnapValue(z));
                        if (TryGetVoxelId(point, out var id, out var rotation))
                        {
                            voxelList.Add(new BuildIslandVoxel
                            {
                                id = id,
                                point = originRotation * (point - originPoint),
                                rotation = rotation
                            });
                        }
                    }
                }
            }

            return new BuildIslandMap
            {
                voxelList = voxelList
            };
        }

        public void SetBuildIslandMap(BuildIslandMap map, Pose originPose)
        {
            SetBuildIslandMapAsync(map, originPose).Forget();
        }

        public void DestroyVoxelsInBounds(Bounds bounds, Action<Vector3> onDestroyed = null)
        {
            const float shift = 0.5f;
            var min = bounds.min;
            var max = bounds.max;

            for (var x = min.x + shift; x < max.x; x++)
            {
                for (var y = min.y + shift; y < max.y; y++)
                {
                    for (var z = min.z + shift; z < max.z; z++)
                    {
                        var point = new Vector3(GetSnapValue(x), GetSnapValue(y), GetSnapValue(z));
                        if (DestroyVoxel(point))
                        {
                            onDestroyed?.Invoke(point);
                        }
                    }
                }
            }
        }

        private async UniTaskVoid SetBuildIslandMapAsync(BuildIslandMap map, Pose originPose)
        {
            const int maxVoxelsPerPatch = 16;
            var counter = maxVoxelsPerPatch;

            foreach (var buildZoneVoxel in map.voxelList)
            {
                if (counter % maxVoxelsPerPatch == 0)
                {
                    await UniTask.Yield(disposeCancellationTokenSource.Token);
                }

                var point = originPose.position + originPose.rotation * buildZoneVoxel.point;
                CreateVoxelNetworked(point, buildZoneVoxel.id, true, buildZoneVoxel.rotation);
                counter++;
            }
        }
    }
}