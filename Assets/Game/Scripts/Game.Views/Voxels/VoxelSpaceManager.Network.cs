using System;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Fusion;
using Modules.Core;
using Modules.Network;
using UnityEngine;

namespace Game.Views.Voxels
{
    public partial class VoxelSpaceManager
    {
        private VoxelsMasterClientObject voxelsMasterClientObject;

        private readonly IAsyncReactiveProperty<int> sliceServerTime = new AsyncReactiveProperty<int>(0);
        private readonly ISubject<CreatingVoxelArgs> onVoxelCreating = new Subject<CreatingVoxelArgs>();
        private readonly ISubject<DestroyingVoxelArgs> onVoxelDestroying = new Subject<DestroyingVoxelArgs>();
        private readonly ISubject<DestroyedVoxelArgs> onVoxelDestroyed = new Subject<DestroyedVoxelArgs>();
        private readonly ISubject<CreatingVoxelArgs> onInitialVoxelCreating = new Subject<CreatingVoxelArgs>();
        private readonly ISubject<DestroyingVoxelArgs> onInitialVoxelDestroying = new Subject<DestroyingVoxelArgs>();
        private readonly ISubject<PlayerRef> onInitialVoxelsSending = new Subject<PlayerRef>();
        private readonly ISubject<PlayerRef> onInitialVoxelsCompleting = new Subject<PlayerRef>();

        private int ServerTime => voxelsMasterClientObject == null ? 0 : Mathf.RoundToInt(voxelsMasterClientObject.Runner.RemoteRenderTime);

        public IReadOnlyAsyncReactiveProperty<int> SliceServerTime => sliceServerTime;
        public IObservable<CreatingVoxelArgs> OnVoxelCreating => onVoxelCreating;
        public IObservable<DestroyingVoxelArgs> OnVoxelDestroying => onVoxelDestroying;
        public IObservable<CreatingVoxelArgs> OnInitialVoxelCreating => onInitialVoxelCreating;
        public IObservable<DestroyingVoxelArgs> OnInitialVoxelDestroying => onInitialVoxelDestroying;
        public IObservable<DestroyedVoxelArgs> OnVoxelDestroyed => onVoxelDestroyed;
        public IObservable<PlayerRef> OnInitialVoxelsSending => onInitialVoxelsSending;
        public IObservable<PlayerRef> OnInitialVoxelsCompleting => onInitialVoxelsCompleting;

        public void CreateVoxelNetworked(Vector3 position, int id, bool ignoreSound, byte rotation = 0)
        {
            voxelsMasterClientObject?.CreateVoxel(position, id, ServerTime, ignoreSound, rotation);
        }

        public void DestroyVoxelNetworked(Vector3 position)
        {
            voxelsMasterClientObject?.DestroyVoxel(position, ServerTime);
        }

        public void CreateInitialVoxelNetworked(PlayerRef player, Vector3 position, int index, int tick)
        {
            voxelsMasterClientObject?.CreateInitialVoxel(player, position, index, tick);
        }

        public void DestroyInitialVoxelNetworked(PlayerRef player, Vector3 position, int tick)
        {
            voxelsMasterClientObject?.DestroyInitialVoxel(player, position, tick);
        }

        public void ReceivingInitialVoxels(PlayerRef player)
        {
            voxelsMasterClientObject?.ReceiveInitialVoxels(player);
        }

        public void CompleteInitialVoxels(PlayerRef player)
        {
            voxelsMasterClientObject?.CompleteInitialVoxels(player);
        }

        public void SetSliceVoxelsServerTime(int sliceServerTime)
        {
            voxelsMasterClientObject?.SetSliceServerTimeTick(sliceServerTime);
        }

        private void AddVoxelsMasterClientObject(NetworkActor actor)
        {
            if (actor is not VoxelsMasterClientObject voxelsMasterClientObject)
            {
                return;
            }

            this.voxelsMasterClientObject = voxelsMasterClientObject;

            voxelsMasterClientObject.SliceServerTime.Subscribe(x => sliceServerTime.Value = x).AddTo(voxelsMasterClientObject);
            voxelsMasterClientObject.OnVoxelCreating.Subscribe(onVoxelCreating.OnNext).AddTo(voxelsMasterClientObject);
            voxelsMasterClientObject.OnVoxelDestroying.Subscribe(onVoxelDestroying.OnNext).AddTo(voxelsMasterClientObject);
            voxelsMasterClientObject.OnInitialVoxelCreating.Subscribe(onVoxelCreating.OnNext).AddTo(voxelsMasterClientObject);
            voxelsMasterClientObject.OnInitialVoxelDestroying.Subscribe(onInitialVoxelDestroying.OnNext).AddTo(voxelsMasterClientObject);
            voxelsMasterClientObject.OnInitialVoxelsSending.Subscribe(onInitialVoxelsSending.OnNext).AddTo(voxelsMasterClientObject);
            voxelsMasterClientObject.OnInitialVoxelsCompleting.Subscribe(onInitialVoxelsCompleting.OnNext).AddTo(voxelsMasterClientObject);
        }

        private void RemoveVoxelsMasterClientObject()
        {
            voxelsMasterClientObject = null;

            if (sliceServerTime.Value != 0)
            {
                sliceServerTime.Value = 0;
            }
        }
    }
}