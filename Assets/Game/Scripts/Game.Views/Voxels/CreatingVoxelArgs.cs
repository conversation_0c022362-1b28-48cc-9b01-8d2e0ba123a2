using UnityEngine;

namespace Game.Views.Voxels
{
    public readonly struct CreatingVoxelArgs
    {
        public readonly Vector3 position;
        public readonly int id;
        public readonly int time;
        public readonly bool ignoreSound;
        public readonly bool byLocalPlayer;
        public readonly byte rotation;

        public CreatingVoxelArgs(Vector3 position, int id, int time, bool ignoreSound, bool byLocalPlayer, byte rotation = 0)
        {
            this.position = position;
            this.id = id;
            this.time = time;
            this.ignoreSound = ignoreSound;
            this.byLocalPlayer = byLocalPlayer;
            this.rotation = rotation;
        }
    }
}