using UnityEngine;

namespace Game.View.Locomotions
{
    public class GrappleHand
    {
        private readonly Transform handNode;
        private readonly GrappleLocomotionConfig config;

        private Vector3 anchor;

        public Vector3 Force { get; private set; }
        public bool IsHooked { get; private set; }

        public GrappleHand(GrappleLocomotionConfig config, Transform handNode)
        {
            this.config = config;
            this.handNode = handNode;
        }

        public void Update()
        {
            if (!IsHooked)
            {
                return;
            }

            UpdateForce();
        }

        public void Hook(Vector3 anchor)
        {
            if (IsHooked)
            {
                return;
            }

            IsHooked = true;
            this.anchor = anchor;
        }

        public void Unhook()
        {
            if (!IsHooked)
            {
                return;
            }

            IsHooked = false;
            Force = Vector3.zero;
        }

        private void UpdateForce()
        {
            var direction = (anchor - handNode.position).normalized;
            Force = direction * config.PullStrength;
        }
    }
}