using System.Collections.Generic;
using Modules.Core;
using UnityEngine;

namespace Game.Views.World
{
    public class WorldBoundary : Actor
    {
        [SerializeField] private List<WorldWall> walls;

        private Bounds worldBounds;

        public void Initialize(int worldSize, int worldHeight, WorldSide holeSide, Vector2 holeSize, float holeHeight)
        {
            var halfWorldSize = worldSize / 2;
            var halfWorldHeight = worldHeight / 2;
            var verticalWorldSize = new Vector2(worldSize, worldHeight);
            var horizontalWorldSize = worldSize * Vector2.one;

            var forwardPosition = new Vector3(0, holeSide == WorldSide.Forward ? holeHeight : halfWorldHeight, halfWorldSize);
            SetupVerticalWall(WorldSide.Forward, forwardPosition, verticalWorldSize, holeSide, holeSize);

            var backPosition = new Vector3(0, holeSide == WorldSide.Back ? holeHeight : halfWorldHeight, -halfWorldSize);
            SetupVerticalWall(WorldSide.Back, backPosition, verticalWorldSize, holeSide, holeSize);

            var leftPosition = new Vector3(-halfWorldSize, holeSide == WorldSide.Left ? holeHeight : halfWorldHeight, 0);
            SetupVerticalWall(WorldSide.Left, leftPosition, verticalWorldSize, holeSide, holeSize);

            var rightPosition = new Vector3(halfWorldSize, holeSide == WorldSide.Right ? holeHeight : halfWorldHeight, 0);
            SetupVerticalWall(WorldSide.Right, rightPosition, verticalWorldSize, holeSide, holeSize);

            var upPosition = new Vector3(0, worldHeight, 0);
            SetupVerticalWall(WorldSide.Up, upPosition, horizontalWorldSize, holeSide, holeSize);

            var downPosition = new Vector3(0, 0, 0);
            SetupVerticalWall(WorldSide.Down, downPosition, horizontalWorldSize, holeSide, holeSize);

            worldBounds = default;
            worldBounds.Encapsulate(forwardPosition);
            worldBounds.Encapsulate(backPosition);
            worldBounds.Encapsulate(leftPosition);
            worldBounds.Encapsulate(rightPosition);
            worldBounds.Encapsulate(upPosition);
            worldBounds.Encapsulate(downPosition);

            EnableBoundary();
        }

        public void DisableBoundary()
        {
            foreach (var wall in walls)
            {
                wall.gameObject.SetActive(false);
            }
        }

        public void EnableBoundary()
        {
            foreach (var wall in walls)
            {
                wall.gameObject.SetActive(true);
            }
        }

        public bool Contains(Vector3 position)
        {
            return worldBounds.Contains(position);
        }

        private void SetupVerticalWall(WorldSide worldSide, Vector3 position, Vector2 worldSize, WorldSide holeSide, Vector2 holeSize)
        {
            if (worldSide == holeSide)
            {
                GetWall(worldSide).Setup(position, worldSize, holeSize);
            }
            else
            {
                GetWall(worldSide).Setup(position, worldSize);
            }
        }

        private WorldWall GetWall(WorldSide side)
        {
            return walls.Find(w => w.WorldSide == side);
        }
    }
}