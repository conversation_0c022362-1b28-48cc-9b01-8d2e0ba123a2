using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Views.Players;
using Game.Views.Shared;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Mirror
{
    public class MirrorView : Actor
    {
        [SerializeField] private GameObject viewNode;
        [SerializeField] private GameObject fakeMirrorObject;
        [SerializeField] private GameObject realMirrorObject;
        [SerializeField] private XRPlayerDetector playerDetector;

        private DistanceCuller distanceCuller;
        private CancellationTokenSource disableCancellationTokenSource;

        [Inject]
        private void Construct(DistanceCuller distanceCuller)
        {
            this.distanceCuller = distanceCuller;
        }

        private void OnEnable()
        {
            disableCancellationTokenSource = new CancellationTokenSource();
            playerDetector.IsDetected.Subscribe(HandlePlayerDetection).AddTo(disableCancellationTokenSource.Token);
            distanceCuller.AddTarget(viewNode, Constants.ObjectCullDistance);
        }

        private void OnDisable()
        {
            disableCancellationTokenSource.CancelAndDispose();
            distanceCuller.RemoveTarget(viewNode);
        }

        private void HandlePlayerDetection(bool isDetected)
        {
            realMirrorObject.SetActive(isDetected);
            fakeMirrorObject.SetActive(!isDetected);
        }
    }
}