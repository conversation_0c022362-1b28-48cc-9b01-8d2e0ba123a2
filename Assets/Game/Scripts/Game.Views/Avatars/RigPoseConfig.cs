using System.Collections.Generic;
using UnityEngine;

namespace Game.Views.Avatars
{
    public class RigPoseConfig : ScriptableObject
    {
        [SerializeField] private List<Pose> poses;

        public List<Pose> Poses => poses;

        public void Save()
        {
#if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            UnityEditor.AssetDatabase.SaveAssetIfDirty(this);
#endif
        }
    }
}