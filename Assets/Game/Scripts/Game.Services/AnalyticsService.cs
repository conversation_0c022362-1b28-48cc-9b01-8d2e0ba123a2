using System.Collections.Generic;
using Modules.Analytics;
using UnityEngine;

namespace Game.Services
{
    internal class AnalyticsService : IAnalyticsService
    {
        private readonly IAnalyticsClient analyticsClient;

        public AnalyticsService(IAnalyticsClient analyticsClient)
        {
            this.analyticsClient = analyticsClient;
        }

        public void Initialize()
        {
            analyticsClient.Initialize();
        }

        public void SetupUser(string userId, string userName)
        {
            analyticsClient.SetUserId(userId);
            analyticsClient.SetUserProperty("$name", userName);
        }

        public void StartLevel(string levelName, string roomId, int playerCount, int gameCount)
        {
            analyticsClient.Track("level_start", new Dictionary<string, object>
            {
                { "level_name", levelName },
                { "room_id", roomId },
                { "player_count", playerCount },
                { "game_count", gameCount },
                { "session_count", analyticsClient.SessionCount }
            });
        }

        public void EndLevel(string levelName, int playerCount, int localtime, string avatar, int fps)
        {
            analyticsClient.Track("level_end", new Dictionary<string, object>
            {
                { "level_name", levelName },
                { "player_count", playerCount },
                { "local_time", localtime },
                { "avatar", avatar },
                { "fps", fps }
            });
        }

        public void Purchase(string title, string category, float usdSpent, int coinSpent, int coinReceived, string source)
        {
            var parameters = new Dictionary<string, object>
            {
                { "title", title },
                { "session_count", analyticsClient.SessionCount }
            };

            if (!string.IsNullOrEmpty(category))
            {
                parameters.Add("category", category);
            }

            if (usdSpent > 0)
            {
                parameters.Add("usd_spent", Mathf.CeilToInt(usdSpent));
            }

            if (coinSpent > 0)
            {
                parameters.Add("coin_spent", coinSpent);
            }

            if (coinReceived > 0)
            {
                parameters.Add("coin_received", coinReceived);
            }

            if (!string.IsNullOrEmpty(source))
            {
                parameters.Add("source", source);
            }

            analyticsClient.Track("purchase", parameters);
        }

        public void BufferShouldBeNullError(string levelName, int playerId, int localTime, int serverTime)
        {
            analyticsClient.Track("buffer_should_be_null", new Dictionary<string, object>
            {
                { "level_name", levelName },
                { "player_id", playerId },
                { "local_time", localTime },
                { "server_time", serverTime }
            });
        }
    }
}