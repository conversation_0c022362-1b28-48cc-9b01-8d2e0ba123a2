using UnityEngine.XR.Interaction.Toolkit.Filtering;

namespace Modules.XR
{
    /// <summary>
    /// Base class for Interaction Toolkit Filter implementation. Implements all interfaces
    /// with virtual methods to easily override only what's needed.
    /// </summary>
    public abstract class BaseXRFilter : IXRHoverFilter, IXRSelectFilter
    {
        protected virtual bool CanHover => true;
        protected virtual bool CanSelect => true;

        protected virtual bool ProcessHover(
            UnityEngine.XR.Interaction.Toolkit.Interactors.IXRHoverInteractor interactor,
            UnityEngine.XR.Interaction.Toolkit.Interactables.IXRHoverInteractable interactable) => true;

        protected virtual bool ProcessSelect(
            UnityEngine.XR.Interaction.Toolkit.Interactors.IXRSelectInteractor interactor,
            UnityEngine.XR.Interaction.Toolkit.Interactables.IXRSelectInteractable interactable) => true;

        bool IXRHoverFilter.canProcess => CanHover;
        bool IXRSelectFilter.canProcess => CanSelect;

        bool IXRHoverFilter.Process(UnityEngine.XR.Interaction.Toolkit.Interactors.IXRHoverInteractor interactor, UnityEngine.XR.Interaction.Toolkit.Interactables.IXRHoverInteractable interactable)
        {
            return ProcessHover(interactor, interactable);
        }

        bool IXRSelectFilter.Process(UnityEngine.XR.Interaction.Toolkit.Interactors.IXRSelectInteractor interactor, UnityEngine.XR.Interaction.Toolkit.Interactables.IXRSelectInteractable interactable)
        {
            return ProcessSelect(interactor, interactable);
        }
    }
}