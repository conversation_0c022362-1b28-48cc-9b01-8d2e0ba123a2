using System.Collections;
using System.Collections.Generic;

namespace Modules.Core
{
    /// <summary>
    /// A queue with a max size. When queue is holding max amount of items
    /// and a new one is enqueued, it also dequeues an item to maintain
    /// it's fixed size.
    /// </summary>
    public class FixedSizeQueue<T> : IEnumerable<T>
    {
        private readonly Queue<T> queue;
        private readonly int maxSize;

        public FixedSizeQueue(int maxSize)
        {
            this.maxSize = maxSize;
            queue = new Queue<T>(maxSize);
        }

        public int Count => queue.Count;
        public T[] ToArray() => queue.ToArray();

        public void Enqueue(T element)
        {
            if (queue.Count == maxSize) queue.Dequeue();
            queue.Enqueue(element);
        }

        public T Dequeue() => queue.Dequeue();
        public T Peek() => queue.Peek();
        public void Clear() => queue.Clear();
        public IEnumerator<T> GetEnumerator() => queue.GetEnumerator();

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }
}