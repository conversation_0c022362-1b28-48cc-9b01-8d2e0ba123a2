using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;
using Unity.Services.RemoteConfig;
using Log = Modules.UnityGameServices.Logger.UnityGameServices;

namespace Modules.UnityGameServices
{
    internal partial class UnityGameServices
    {
        private static RemoteConfigService Service => RemoteConfigService.Instance;
        private static RuntimeConfig Config => Service.appConfig;

        public async UniTask<Result> GetRemoteConfig(CancellationToken cancellationToken)
        {
            try
            {
                await Service.FetchConfigsAsync(new UserAttributes(), new AppAttributes()).AsUniTask().AttachExternalCancellation(cancellationToken);
                Log.Debug("Remote config: {0}", Config.config.ToString());
            }
            catch (OperationCanceledException exception)
            {
                return LogWarnAndGetResult(new Error(nameof(OperationCanceledException), exception.Message));
            }
            catch (Exception exception)
            {
                return LogFatalAndGetResult(new Error(nameof(Exception), exception.Message));
            }

            return Result.Ok();
        }

        public bool TryGetJsonConfig(string key, out string result)
        {
            if (Config.HasKey(key))
            {
                result = Config.GetJson(key, string.Empty);
                return true;
            }

            result = string.Empty;
            return false;
        }

        public bool TryGetStringConfig(string key, out string result)
        {
            if (Config.HasKey(key))
            {
                result = Config.GetString(key, string.Empty);
                return true;
            }

            result = string.Empty;
            return false;
        }

        public bool TryGetBoolConfig(string key, out bool result)
        {
            if (Config.HasKey(key))
            {
                result = Config.GetBool(key);
                return true;
            }

            result = false;
            return false;
        }

        public bool TryGetFloatConfig(string key, out float result)
        {
            if (Config.HasKey(key))
            {
                result = Config.GetFloat(key);
                return true;
            }

            result = 0;
            return false;
        }

        public bool TryGetIntConfig(string key, out int result)
        {
            if (Config.HasKey(key))
            {
                result = Config.GetInt(key);
                return true;
            }

            result = 0;
            return false;
        }

        public struct UserAttributes
        {
        }

        public struct AppAttributes
        {
        }
    }
}