using System.Collections.Generic;

namespace Modules.UI
{
    internal class ScreenManager : IScreenManager
    {
        private readonly List<ScreensContainer> screensContainers = new();

        public T GetScreen<T>(bool hide = false) where T : Screen
        {
            for (var i = 0; i < screensContainers.Count; i++)
            {
                var screensContainer = screensContainers[i];
                var screen = screensContainer.GetScreen<T>(hide);

                if (screen != null)
                {
                    return screen;
                }
            }

            return null;
        }

        public void AddScreensContainer(ScreensContainer screensContainer)
        {
            if (screensContainers.Contains(screensContainer))
            {
                Log.UI.Warn("ScreensContainer with name {0} already added", screensContainer.name);
                return;
            }

            screensContainers.Add(screensContainer);
            Log.UI.Debug("Add ScreensContainer: {0}", screensContainer.name);
        }

        public void RemoveScreensContainer(ScreensContainer screensContainer)
        {
            if (!screensContainers.Contains(screensContainer))
            {
                Log.UI.Debug("ScreensContainer with name {0} have not added", screensContainer.name);
                return;
            }

            screensContainers.Remove(screensContainer);
            Log.UI.Debug("Remove ScreensContainer: {0}", screensContainer.name);
        }
    }
}